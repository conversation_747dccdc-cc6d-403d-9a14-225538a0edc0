@echo off
title Aplikasi SPP - Quick Start
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    APLIKASI SPP - QUICK START                ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo  [1] Checking XAMPP Services...
echo.

REM Check if XAMPP processes are running
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ Apache is running
    set APACHE_OK=1
) else (
    echo  ✗ Apache is not running
    set APACHE_OK=0
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ MySQL is running
    set MYSQL_OK=1
) else (
    echo  ✗ MySQL is not running
    set MYSQL_OK=0
)

echo.

if %APACHE_OK%==0 (
    echo  ⚠️  Apache tidak berjalan!
    echo  💡 Solusi: Buka XAMPP Control Panel dan start Apache
    echo.
)

if %MYSQL_OK%==0 (
    echo  ⚠️  MySQL tidak berjalan!
    echo  💡 Solusi: Buka XAMPP Control Panel dan start MySQL
    echo.
)

if %APACHE_OK%==1 if %MYSQL_OK%==1 (
    echo  [2] Starting PHP Development Server...
    echo.
    
    if exist "C:\xampp\php\php.exe" (
        echo  🚀 Server starting at: http://localhost:8000
        echo  📱 Login: admin/admin atau petugas/petugas
        echo.
        echo  ╔══════════════════════════════════════════════════════════════╗
        echo  ║  SERVER BERJALAN! Buka browser: http://localhost:8000       ║
        echo  ║  Tekan Ctrl+C untuk stop server                             ║
        echo  ╚══════════════════════════════════════════════════════════════╝
        echo.
        
        REM Open browser
        start http://localhost:8000
        
        REM Start PHP server
        "C:\xampp\php\php.exe" -S localhost:8000
    ) else (
        echo  ❌ PHP tidak ditemukan di C:\xampp\php\php.exe
        echo  💡 Pastikan XAMPP terinstall dengan benar
        echo.
    )
) else (
    echo  [2] Cannot start server - XAMPP services not running
    echo.
    echo  📋 LANGKAH YANG HARUS DILAKUKAN:
    echo  1. Buka XAMPP Control Panel
    echo  2. Klik "Start" untuk Apache
    echo  3. Klik "Start" untuk MySQL
    echo  4. Jalankan script ini lagi
    echo.
    echo  🌐 ALTERNATIF: Gunakan XAMPP htdocs
    echo  1. Copy folder project ke: C:\xampp\htdocs\
    echo  2. Akses: http://localhost/aplikasi-spp
    echo.
)

echo.
pause
