@echo off
echo ========================================
echo    TEST DATABASE CONNECTION
echo ========================================
echo.

REM Try different PHP paths
set PHP_FOUND=0

echo Mencari PHP...

REM Check XAMPP default path
if exist "C:\xampp\php\php.exe" (
    echo ✓ PHP ditemukan di: C:\xampp\php\php.exe
    echo.
    echo Testing database...
    "C:\xampp\php\php.exe" test-db-simple.php
    set PHP_FOUND=1
    goto end
)

REM Check if PHP in PATH
php --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ PHP ditemukan di PATH
    echo.
    echo Testing database...
    php test-db-simple.php
    set PHP_FOUND=1
    goto end
)

REM Check other common paths
if exist "D:\xampp\php\php.exe" (
    echo ✓ PHP ditemukan di: D:\xampp\php\php.exe
    echo.
    echo Testing database...
    "D:\xampp\php\php.exe" test-db-simple.php
    set PHP_FOUND=1
    goto end
)

if exist "C:\Program Files\xampp\php\php.exe" (
    echo ✓ PHP ditemukan di: C:\Program Files\xampp\php\php.exe
    echo.
    echo Testing database...
    "C:\Program Files\xampp\php\php.exe" test-db-simple.php
    set PHP_FOUND=1
    goto end
)

echo ❌ PHP tidak ditemukan!
echo.
echo SOLUSI:
echo 1. Pastikan XAMPP terinstall
echo 2. Atau test manual via browser setelah server jalan
echo.

:end
if %PHP_FOUND%==0 (
    echo.
    echo ALTERNATIF: Test via browser setelah server jalan
    echo Akses: http://localhost:8000/test-setup.php
)

echo.
pause
