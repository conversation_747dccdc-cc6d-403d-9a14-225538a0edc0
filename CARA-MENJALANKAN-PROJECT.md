# 🚀 CARA MENJALANKAN PROJECT SPP - PANDUAN LENGKAP

## 📋 RINGKASAN CEPAT

**Untuk menjalankan project ini di masa depan, ikuti 3 langkah sederhana:**

1. **Start XAMPP:** Buka XAMPP Control Panel → Start Apache & MySQL
2. **Start Server:** Buka PowerShell di folder project → Jalankan `C:\xampp\php\php.exe -S localhost:8000`
3. **Akses Aplikasi:** Buka browser → http://localhost:8000 → Login: admin/admin

---

## 🔧 SETUP AWAL (Hanya Sekali)

### Persyaratan:
- ✅ XAMPP terinstall
- ✅ Database 'spp' sudah diimport
- ✅ Project folder di: `d:\Tugasakhir\Githubb\aplikasi-spp`

### Jika Belum Setup:
1. **Install XAMPP:** https://www.apachefriends.org/download.html
2. **Import Database:** 
   - <PERSON>uka http://localhost/phpmyadmin
   - Import file `database.sql`

---

## 🚀 CARA MENJALANKAN (Setiap Kali)

### Metode 1: Manual (Recommended)

**1. Start XAMPP Services:**
```
- Buka XAMPP Control Panel
- Klik "Start" untuk Apache
- Klik "Start" untuk MySQL
- Pastikan keduanya berwarna hijau
```

**2. Start PHP Server:**
```powershell
# Buka PowerShell di folder project
cd "d:\Tugasakhir\Githubb\aplikasi-spp"

# Jalankan server
C:\xampp\php\php.exe -S localhost:8000
```

**3. Akses Aplikasi:**
```
- Buka browser
- Akses: http://localhost:8000
- Login: admin/admin atau petugas/petugas
```

### Metode 2: Menggunakan Script

**1. Start XAMPP (manual)**

**2. Jalankan Script:**
```powershell
.\start-server-xampp.bat
```

### Metode 3: Via XAMPP htdocs

**1. Copy project ke XAMPP:**
```
Copy folder project ke: C:\xampp\htdocs\aplikasi-spp
```

**2. Akses via browser:**
```
http://localhost/aplikasi-spp
```

---

## 🔐 AKUN LOGIN

| Role | Username | Password | Akses |
|------|----------|----------|-------|
| Admin | admin | admin | Full access |
| Petugas | petugas | petugas | Limited access |

---

## 🧪 TESTING & VERIFIKASI

### Test Setup Sistem:
```powershell
C:\xampp\php\php.exe test-db-simple.php
```

### Test via Browser:
- http://localhost:8000/test-setup.php
- http://localhost:8000/test-connection.php

---

## ❌ TROUBLESHOOTING

### Server tidak bisa start:
```
✓ Pastikan XAMPP Apache & MySQL running
✓ Pastikan port 8000 tidak digunakan aplikasi lain
✓ Coba port lain: php -S localhost:8080
```

### Database error:
```
✓ Pastikan MySQL di XAMPP running
✓ Cek database 'spp' ada di phpMyAdmin
✓ Re-import database.sql jika perlu
```

### Login gagal:
```
✓ Pastikan database sudah diimport
✓ Cek tabel 'petugas' ada data
✓ Username: admin, Password: admin
```

### PHP tidak ditemukan:
```
✓ Gunakan path lengkap: C:\xampp\php\php.exe
✓ Atau tambahkan C:\xampp\php ke PATH
✓ Atau gunakan metode htdocs
```

---

## 📁 STRUKTUR PROJECT

```
aplikasi-spp/
├── index.php              # Halaman utama
├── database.sql           # Database schema
├── app/core/              # Core aplikasi
│   ├── Connection.php     # Konfigurasi database
│   └── ...
├── admin/                 # Panel admin
├── src/                   # JavaScript
└── [script bantuan]       # File .bat dan .ps1
```

---

## 🎯 FITUR APLIKASI

### Halaman Utama:
- Cek pembayaran SPP via NISN
- Login petugas/admin

### Dashboard Admin:
- Overview data sekolah
- Statistik pembayaran

### Manajemen Data:
- Data Siswa (CRUD)
- Data Kelas (CRUD)
- Data SPP (CRUD)
- Data Petugas (CRUD)

### Pembayaran:
- Input pembayaran SPP
- History pembayaran
- Generate laporan

---

## 🔄 WORKFLOW PENGGUNAAN

1. **Login** sebagai admin/petugas
2. **Setup Master Data:**
   - Tambah data kelas
   - Tambah data SPP (nominal per tahun)
   - Tambah data siswa
3. **Proses Pembayaran:**
   - Input pembayaran SPP siswa
   - Print bukti pembayaran
4. **Laporan:**
   - Generate laporan pembayaran
   - Export data

---

## 📞 BANTUAN

**File bantuan yang tersedia:**
- `IKUTI-LANGKAH-INI.md` - Panduan step-by-step
- `MULAI-DISINI.txt` - Quick start guide
- `test-setup.php` - Test sistem
- `start-server-xampp.bat` - Script start server

**Jika masih ada masalah:**
1. Cek file test-setup.php di browser
2. Pastikan XAMPP services running
3. Restart XAMPP jika perlu
4. Re-import database jika perlu

---

## ✅ CHECKLIST SEBELUM RUNNING

- [ ] XAMPP terinstall
- [ ] Apache & MySQL running (hijau)
- [ ] Database 'spp' sudah diimport
- [ ] Project folder di lokasi yang benar
- [ ] Browser siap digunakan

**🎉 Happy Coding!**
