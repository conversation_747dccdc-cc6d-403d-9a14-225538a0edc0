@echo off
echo ========================================
echo    CHECK MYSQL PORT CONFLICT
echo ========================================
echo.

echo Checking what's using port 3306...
netstat -ano | findstr :3306

echo.
echo Checking what's using port 3307...
netstat -ano | findstr :3307

echo.
echo If port 3306 is used by another process:
echo 1. Stop that process
echo 2. Or change MySQL port in XAMPP
echo.

echo To change MySQL port:
echo 1. Open XAMPP Control Panel
echo 2. Click "Config" next to MySQL
echo 3. Select "my.ini"
echo 4. Find "port = 3306"
echo 5. Change to "port = 3307"
echo 6. Save and restart MySQL
echo.

pause
