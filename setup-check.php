<?php
/**
 * Script untuk mengecek persyaratan sistem dan setup aplikasi SPP
 */

echo "<h1>Setup Check - Aplikasi SPP</h1>";
echo "<hr>";

// Check PHP Version
echo "<h2>1. PHP Version Check</h2>";
$php_version = phpversion();
$min_php_version = '7.4.0';

if (version_compare($php_version, $min_php_version, '>=')) {
    echo "<p style='color: green;'>✓ PHP Version: $php_version (OK)</p>";
} else {
    echo "<p style='color: red;'>✗ PHP Version: $php_version (Minimum required: $min_php_version)</p>";
}

// Check Required Extensions
echo "<h2>2. Required PHP Extensions</h2>";
$required_extensions = ['mysqli', 'session', 'json'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext extension loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext extension missing</p>";
        $missing_extensions[] = $ext;
    }
}

// Check File Permissions
echo "<h2>3. File Permissions Check</h2>";
$important_files = [
    'index.php',
    'app/core/Connection.php',
    'admin/dashboard.php',
    'database.sql'
];

foreach ($important_files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✓ $file (readable)</p>";
        } else {
            echo "<p style='color: red;'>✗ $file (not readable)</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ $file (file not found)</p>";
    }
}

// Database Connection Test
echo "<h2>4. Database Connection Test</h2>";
$host = "localhost";
$username = "root";
$password = "";
$dbName = "spp";

try {
    $conn = new mysqli($host, $username, $password);
    
    if ($conn->connect_error) {
        throw new Exception("MySQL connection failed: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✓ MySQL connection successful</p>";
    
    // Check if database exists
    $db_check = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbName'");
    if ($db_check->num_rows > 0) {
        echo "<p style='color: green;'>✓ Database '$dbName' exists</p>";
        
        // Connect to the specific database
        $conn->select_db($dbName);
        
        // Check tables
        $tables = ['kelas', 'petugas', 'siswa', 'spp', 'pembayaran'];
        $existing_tables = [];
        $missing_tables = [];
        
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                $existing_tables[] = $table;
                echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            } else {
                $missing_tables[] = $table;
                echo "<p style='color: red;'>✗ Table '$table' missing</p>";
            }
        }
        
        if (empty($missing_tables)) {
            echo "<p style='color: green;'><strong>✓ All required tables exist!</strong></p>";
        } else {
            echo "<p style='color: orange;'>⚠ Missing tables: " . implode(', ', $missing_tables) . "</p>";
            echo "<p>Please import database.sql file</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database '$dbName' does not exist</p>";
        echo "<p>Please create database and import database.sql file</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Summary
echo "<h2>5. Setup Summary</h2>";
$issues = [];

if (version_compare($php_version, $min_php_version, '<')) {
    $issues[] = "PHP version too old";
}

if (!empty($missing_extensions)) {
    $issues[] = "Missing PHP extensions: " . implode(', ', $missing_extensions);
}

if (empty($issues)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>✓ Setup Ready!</h3>";
    echo "<p style='color: #155724; margin: 5px 0 0 0;'>Your system meets all requirements. You can now run the application.</p>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure MySQL/MariaDB is running</li>";
    echo "<li>Import database.sql if you haven't already</li>";
    echo "<li>Run the application using one of these methods:</li>";
    echo "<ul>";
    echo "<li><strong>PHP Built-in Server:</strong> <code>php -S localhost:8000</code></li>";
    echo "<li><strong>XAMPP/WAMP:</strong> Copy to htdocs/www folder and access via browser</li>";
    echo "</ul>";
    echo "</ol>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>✗ Setup Issues Found</h3>";
    echo "<p style='color: #721c24; margin: 5px 0 0 0;'>Please fix the following issues:</p>";
    echo "<ul style='color: #721c24;'>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Generated on: " . date('Y-m-d H:i:s') . "</em></p>";
?>
