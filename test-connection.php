<?php
/**
 * File untuk test koneksi database
 * Jalankan file ini untuk memastikan koneksi database berhasil
 */

echo "<h2>Test Koneksi Database Aplikasi SPP</h2>";

// Konfigurasi database (sama dengan Connection.php)
$host = "localhost";
$username = "root";
$password = "";
$dbName = "spp";

try {
    // Test koneksi menggunakan MySQLi
    $conn = new mysqli($host, $username, $password, $dbName);
    
    if ($conn->connect_error) {
        throw new Exception("Koneksi gagal: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✓ Koneksi database berhasil!</p>";
    
    // Test apakah tabel-tabel sudah ada
    $tables = ['kelas', 'petugas', 'siswa', 'spp', 'pembayaran'];
    $missing_tables = [];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            $missing_tables[] = $table;
        } else {
            echo "<p style='color: green;'>✓ Tabel '$table' ditemukan</p>";
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<p style='color: red;'>✗ Tabel yang hilang: " . implode(', ', $missing_tables) . "</p>";
        echo "<p style='color: orange;'>Silakan import file database.sql terlebih dahulu</p>";
    } else {
        echo "<p style='color: green;'><strong>✓ Semua tabel database sudah tersedia!</strong></p>";
        
        // Test data sample
        $petugas_count = $conn->query("SELECT COUNT(*) as count FROM petugas")->fetch_assoc()['count'];
        $siswa_count = $conn->query("SELECT COUNT(*) as count FROM siswa")->fetch_assoc()['count'];
        $kelas_count = $conn->query("SELECT COUNT(*) as count FROM kelas")->fetch_assoc()['count'];
        
        echo "<h3>Data yang tersedia:</h3>";
        echo "<ul>";
        echo "<li>Petugas: $petugas_count</li>";
        echo "<li>Siswa: $siswa_count</li>";
        echo "<li>Kelas: $kelas_count</li>";
        echo "</ul>";
        
        if ($petugas_count > 0) {
            echo "<h3>Akun Login yang tersedia:</h3>";
            $petugas_result = $conn->query("SELECT username, level FROM petugas");
            echo "<ul>";
            while ($row = $petugas_result->fetch_assoc()) {
                echo "<li>Username: <strong>" . $row['username'] . "</strong> (Level: " . $row['level'] . ")</li>";
            }
            echo "</ul>";
            echo "<p style='color: blue;'><em>Password default untuk semua akun adalah sama dengan username</em></p>";
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<h3>Solusi yang mungkin:</h3>";
    echo "<ul>";
    echo "<li>Pastikan MySQL/MariaDB sudah berjalan</li>";
    echo "<li>Periksa username dan password database di file ini</li>";
    echo "<li>Pastikan database 'spp' sudah dibuat</li>";
    echo "<li>Import file database.sql jika belum</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>Informasi PHP:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQL Extension: " . (extension_loaded('mysqli') ? '✓ Tersedia' : '✗ Tidak tersedia') . "</p>";

?>
