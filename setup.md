# Setup Aplikasi SPP

## Persyaratan Sistem
- PHP 7.4 atau lebih tinggi
- MySQL/MariaDB
- Web Server (Apache/Nginx) atau PHP Built-in Server
- Browser modern

## Quick Start (Cara Tercepat)

### Untuk Windows:
1. **Setup Database:**
   ```bash
   # Double-click file: import-database.bat
   # Atau jalankan di command prompt:
   import-database.bat
   ```

2. **Jalankan Server:**
   ```bash
   # Double-click file: start-server.bat
   # Atau jalankan di command prompt:
   start-server.bat
   ```

3. **Akses Aplikasi:**
   - Buka browser: http://localhost:8000
   - Login dengan: admin/admin atau petugas/petugas

### Untuk Linux/Mac:
```bash
# 1. Import database
mysql -u root -p < database.sql

# 2. Jalankan server
php -S localhost:8000

# 3. Buka browser: http://localhost:8000
```

## Langkah-langkah Setup Detail

### 1. Setup Database
1. Pastikan MySQL/MariaDB sudah terinstall dan berjalan
2. Buka terminal/command prompt
3. Login ke MySQL:
   ```bash
   mysql -u root -p
   ```
4. Import database:
   ```bash
   mysql -u root -p < database.sql
   ```
   
   Atau jika menggunakan phpMyAdmin:
   - Buka phpMyAdmin di browser (http://localhost/phpmyadmin)
   - Klik "Import"
   - Pilih file `database.sql`
   - Klik "Go"

### 2. Konfigurasi Database
File konfigurasi database ada di `app/core/Connection.php`:
```php
$host = "localhost";
$username = "root";
$password = "";  // Sesuaikan dengan password MySQL Anda
$dbName = "spp";
```

### 3. Menjalankan Aplikasi

#### Opsi 1: Menggunakan PHP Built-in Server (Recommended untuk development)
```bash
php -S localhost:8000
```
Kemudian buka browser dan akses: http://localhost:8000

#### Opsi 2: Menggunakan XAMPP/WAMP/LAMP
1. Copy folder project ke htdocs (XAMPP) atau www (WAMP)
2. Start Apache dan MySQL dari control panel
3. Buka browser dan akses: http://localhost/aplikasi-spp

### 4. Login ke Aplikasi
Gunakan akun default yang sudah ada di database:

**Admin:**
- Username: admin
- Password: admin

**Petugas:**
- Username: petugas  
- Password: petugas

### 5. Fitur Aplikasi
- **Halaman Utama**: Cek pembayaran SPP siswa dengan NISN
- **Dashboard Admin/Petugas**: Manajemen data siswa, kelas, SPP, dan pembayaran
- **Laporan**: Generate laporan pembayaran

## Testing & Verification

### 1. Test Setup Sistem
Jalankan file berikut untuk mengecek persyaratan sistem:
```bash
php setup-check.php
```
Atau buka di browser: http://localhost:8000/setup-check.php

### 2. Test Koneksi Database
```bash
php test-connection.php
```
Atau buka di browser: http://localhost:8000/test-connection.php

## Troubleshooting

### Error Koneksi Database
- **Pastikan MySQL/MariaDB berjalan**
  - Windows (XAMPP): Start MySQL dari XAMPP Control Panel
  - Windows (Standalone): `net start mysql`
  - Linux: `sudo systemctl start mysql`
  - Mac: `brew services start mysql`

- **Periksa konfigurasi database**
  - File: `app/core/Connection.php`
  - Default: host=localhost, user=root, password=(kosong)

- **Import database jika belum**
  - Gunakan: `import-database.bat` (Windows)
  - Atau: `mysql -u root -p < database.sql`

### Error PHP Not Found
- **Install PHP** dari https://www.php.net/downloads
- **Tambahkan PHP ke PATH** environment variable
- **Atau gunakan XAMPP/WAMP** yang sudah include PHP

### Error 404 Not Found
- Pastikan menjalankan server dari root folder project
- Untuk PHP built-in server: `php -S localhost:8000`
- Untuk XAMPP: copy ke folder `htdocs`

### Error Permission Denied
- Windows: Run command prompt as Administrator
- Linux/Mac: `chmod -R 755 aplikasi-spp`

### Port 8000 Already in Use
- Gunakan port lain: `php -S localhost:8080`
- Atau stop aplikasi yang menggunakan port 8000

### Browser Tidak Bisa Akses
- Pastikan firewall tidak memblokir port
- Coba akses: http://127.0.0.1:8000
- Restart browser jika perlu
