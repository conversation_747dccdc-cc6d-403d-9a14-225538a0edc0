# 🚀 PANDUAN MENJALANKAN APLIKASI SPP - WINDOWS

## ⚡ CARA TERCEPAT (Recommended)

### 1. Install XAMPP (All-in-One Solution)
- Download XAMPP: https://www.apachefriends.org/download.html
- Install XAMPP (akan menginstall PHP, MySQL, Apache sekaligus)
- Buka XAMPP Control Panel
- Start **Apache** dan **MySQL**

### 2. Setup Database
Buka Command Prompt atau PowerShell di folder project ini, lalu jalankan:

```powershell
# Untuk PowerShell (yang sedang Anda gunakan):
.\import-database.bat

# Atau manual:
mysql -u root -p < database.sql
```

### 3. Jalankan Aplikasi
```powershell
# Untuk PowerShell:
.\start-server.bat

# Atau manual:
php -S localhost:8000
```

### 4. Akses Aplikasi
- Buka browser: **http://localhost:8000**
- <PERSON>gin dengan:
  - **Admin:** username=`admin`, password=`admin`
  - **Petugas:** username=`petugas`, password=`petugas`

---

## 🔧 TROUBLESHOOTING

### Jika PHP tidak ditemukan:
1. Install XAMPP dari link di atas
2. Atau download PHP standalone: https://www.php.net/downloads
3. Tambahkan PHP ke PATH environment variable

### Jika MySQL tidak ditemukan:
1. Pastikan XAMPP MySQL sudah running
2. Atau install MySQL standalone: https://dev.mysql.com/downloads/

### Jika ada error "command not found":
- Di PowerShell, gunakan `.\nama-file.bat` bukan `nama-file.bat`
- Contoh: `.\import-database.bat`

---

## 📋 LANGKAH DETAIL UNTUK ANDA

**Karena Anda sudah di PowerShell, ikuti langkah ini:**

1. **Cek apakah PHP tersedia:**
   ```powershell
   php --version
   ```

2. **Jika PHP tidak ada, install XAMPP:**
   - Download: https://www.apachefriends.org/download.html
   - Install dan start Apache + MySQL

3. **Import database:**
   ```powershell
   .\import-database.bat
   ```

4. **Start server:**
   ```powershell
   .\start-server.bat
   ```

5. **Buka browser:** http://localhost:8000

---

## 🎯 ALTERNATIF JIKA XAMPP TIDAK BISA

### Menggunakan WAMP/LAMP:
1. Download WAMP: http://www.wampserver.com/
2. Install dan start services
3. Copy folder project ke `www` folder
4. Import database via phpMyAdmin
5. Akses: http://localhost/aplikasi-spp

### Menggunakan Laragon:
1. Download Laragon: https://laragon.org/download/
2. Install dan start
3. Copy project ke `www` folder
4. Auto-setup database
5. Akses via browser

---

## ✅ VERIFIKASI SETUP

Setelah setup, test dengan file-file ini:
- `php setup-check.php` - Cek sistem
- `php test-connection.php` - Test database
- Buka http://localhost:8000/setup-check.php di browser
