@echo off
echo ========================================
echo    Aplikasi SPP - Database Import
echo ========================================
echo.

REM Check if mysql command is available
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL command line tidak ditemukan!
    echo.
    echo Solusi:
    echo 1. Install MySQL/MariaDB
    echo 2. Atau gunakan XAMPP/WAMP dan import melalui phpMyAdmin
    echo 3. Tambahkan MySQL bin folder ke PATH environment variable
    echo.
    echo Untuk import manual via phpMyAdmin:
    echo 1. Buka http://localhost/phpmyadmin
    echo 2. Klik "Import"
    echo 3. Pilih file database.sql
    echo 4. Klik "Go"
    echo.
    pause
    exit /b 1
)

echo MySQL ditemukan:
mysql --version
echo.

echo Mengimport database...
echo.

REM Prompt for MySQL password
set /p mysql_password="Masukkan password MySQL (kosong jika tidak ada): "

if "%mysql_password%"=="" (
    mysql -u root < database.sql
) else (
    mysql -u root -p%mysql_password% < database.sql
)

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✓ Database berhasil diimport!
    echo ========================================
    echo.
    echo Database 'spp' telah dibuat dengan data berikut:
    echo - Tabel: kelas, petugas, siswa, spp, pembayaran
    echo - Akun login default:
    echo   * Admin: username=admin, password=admin
    echo   * Petugas: username=petugas, password=petugas
    echo.
    echo Selanjutnya jalankan: start-server.bat
    echo Atau: php -S localhost:8000
    echo.
) else (
    echo.
    echo ========================================
    echo ✗ Import database gagal!
    echo ========================================
    echo.
    echo Kemungkinan penyebab:
    echo 1. Password MySQL salah
    echo 2. MySQL service tidak berjalan
    echo 3. File database.sql tidak ditemukan
    echo.
    echo Coba import manual via phpMyAdmin
    echo.
)

pause
