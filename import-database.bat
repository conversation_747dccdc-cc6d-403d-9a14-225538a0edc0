@echo off
echo ========================================
echo    Aplikasi SPP - Database Import
echo ========================================
echo.

REM Check if mysql command is available
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL command line tidak ditemukan!
    echo.
    echo SOLUSI MUDAH:
    echo 1. Install XAMPP dari: https://www.apachefriends.org/download.html
    echo 2. Start MySQL dari XAMPP Control Panel
    echo 3. Buka phpMyAdmin: http://localhost/phpmyadmin
    echo 4. Klik "Import" - Pilih file "database.sql" - Klik "Go"
    echo.
    echo ATAU gunakan cara manual:
    echo 1. Buka phpMyAdmin di browser
    echo 2. Klik "Import"
    echo 3. Pilih file database.sql
    echo 4. Klik "Go"
    echo.
    echo Setelah import berhasil, jalankan: start-server.bat
    echo.
    pause
    exit /b 1
)

echo ✓ MySQL ditemukan:
mysql --version
echo.

echo Mengimport database SPP...
echo.

REM Try import without password first
echo Mencoba import tanpa password...
mysql -u root < database.sql 2>nul
if %errorlevel% equ 0 (
    goto success
)

REM If failed, ask for password
echo.
echo Import tanpa password gagal. Mencoba dengan password...
set /p mysql_password="Masukkan password MySQL: "

mysql -u root -p%mysql_password% < database.sql
if %errorlevel% equ 0 (
    goto success
) else (
    goto failed
)

:success
echo.
echo ========================================
echo ✓ DATABASE BERHASIL DIIMPORT!
echo ========================================
echo.
echo Database 'spp' telah dibuat dengan:
echo ✓ Tabel: kelas, petugas, siswa, spp, pembayaran
echo ✓ Data sample siswa dan petugas
echo.
echo AKUN LOGIN:
echo • Admin: username=admin, password=admin
echo • Petugas: username=petugas, password=petugas
echo.
echo LANGKAH SELANJUTNYA:
echo 1. Jalankan: start-server.bat
echo 2. Buka browser: http://localhost:8000
echo 3. Login dengan akun di atas
echo.
goto end

:failed
echo.
echo ========================================
echo ✗ IMPORT DATABASE GAGAL!
echo ========================================
echo.
echo SOLUSI ALTERNATIF - Import Manual:
echo 1. Buka browser: http://localhost/phpmyadmin
echo 2. Klik "Import"
echo 3. Choose File: pilih "database.sql"
echo 4. Klik "Go"
echo.
echo Jika phpMyAdmin tidak bisa diakses:
echo 1. Pastikan XAMPP MySQL sudah running
echo 2. Atau install XAMPP dari: https://www.apachefriends.org/
echo.

:end
pause
