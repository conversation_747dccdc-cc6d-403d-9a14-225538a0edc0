@echo off
echo ========================================
echo    Aplikasi SPP - Starting Server (XAMPP)
echo ========================================
echo.

REM Try different PHP paths
set PHP_PATH=""

REM Check XAMPP default path
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    echo ✓ PHP ditemukan di: C:\xampp\php\php.exe
    goto start_server
)

REM Check if PHP in PATH
php --version >nul 2>&1
if %errorlevel% equ 0 (
    set PHP_PATH="php"
    echo ✓ PHP ditemukan di PATH
    goto start_server
)

REM Check other common XAMPP locations
if exist "D:\xampp\php\php.exe" (
    set PHP_PATH="D:\xampp\php\php.exe"
    echo ✓ PHP ditemukan di: D:\xampp\php\php.exe
    goto start_server
)

if exist "C:\Program Files\xampp\php\php.exe" (
    set PHP_PATH="C:\Program Files\xampp\php\php.exe"
    echo ✓ PHP ditemukan di: C:\Program Files\xampp\php\php.exe
    goto start_server
)

echo ❌ PHP tidak ditemukan!
echo.
echo SOLUSI ALTERNATIF:
echo 1. Pastikan XAMPP sudah terinstall
echo 2. Copy folder project ini ke: C:\xampp\htdocs\
echo 3. Akses via browser: http://localhost/aplikasi-spp
echo.
echo ATAU tambahkan PHP ke PATH:
echo 1. Buka System Properties > Environment Variables
echo 2. Edit PATH, tambahkan: C:\xampp\php
echo 3. Restart PowerShell
echo.
pause
exit /b 1

:start_server
echo.
echo Mengecek versi PHP...
%PHP_PATH% --version | findstr "PHP"
echo.

echo Memulai PHP Development Server...
echo.
echo ========================================
echo   SERVER BERJALAN DI: http://localhost:8000
echo ========================================
echo.
echo CARA MENGGUNAKAN:
echo 1. Buka browser
echo 2. Akses: http://localhost:8000
echo 3. Login dengan:
echo    • Admin: admin/admin
echo    • Petugas: petugas/petugas
echo.
echo Tekan Ctrl+C untuk menghentikan server
echo ========================================
echo.

REM Start PHP built-in server
%PHP_PATH% -S localhost:8000
