# Aplikasi Pembayaran SPP

Aplikasi web untuk mengelola pembayaran SPP (Sumbangan Pembinaan Pendidikan) sekolah yang dibuat dengan PHP dan MySQL.

## 🚀 Quick Start

### Windows (Cara Termudah)
1. **Import Database:** Double-click `import-database.bat`
2. **Start Server:** Double-click `start-server.bat`
3. **<PERSON><PERSON><PERSON> Aplikasi:** <PERSON>uka http://localhost:8000

### Linux/Mac
```bash
mysql -u root -p < database.sql
php -S localhost:8000
```

## 👤 Login Default

**Admin:**
- Username: `admin`
- Password: `admin`

**Petugas:**
- Username: `petugas`
- Password: `petugas`

## ✨ Fitur Utama

1. **CRUD Data Siswa** - Kelola data siswa dan NISN
2. **CRUD Data Kelas** - Manajemen kelas dan kompetensi keahlian
3. **CRUD Data SPP** - Pengaturan nominal SPP per tahun
4. **CRUD Data Petugas** - Manajemen user admin dan petugas
5. **Transaksi Pem<PERSON>aran** - Input dan tracking pembayaran SPP
6. **Cetak Laporan** - Print data kelas, siswa, dan petugas
7. **Generate Laporan** - Laporan pembayaran SPP
8. **Cek Pembayaran** - Siswa dapat cek status pembayaran via NISN

## 🛠️ Persyaratan Sistem

- PHP 7.4+
- MySQL/MariaDB
- Web Server (Apache/Nginx) atau PHP Built-in Server
- Browser modern

## 📋 Panduan Lengkap

Lihat file `setup.md` untuk panduan instalasi lengkap dan troubleshooting.

## 🧪 Testing

- **Test Setup:** `php setup-check.php`
- **Test Database:** `php test-connection.php`
