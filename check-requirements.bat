@echo off
echo ========================================
echo    CEK PERSYARATAN SISTEM - SPP APP
echo ========================================
echo.

echo [1/3] Mengecek PHP...
php --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ PHP ditemukan:
    php --version | findstr "PHP"
    set PHP_OK=1
) else (
    echo ✗ PHP tidak ditemukan!
    echo.
    echo SOLUSI: Install PHP atau XAMPP
    echo 1. Download XAMPP dari: https://www.apachefriends.org/download.html
    echo 2. Install XAMPP (include PHP, MySQL, Apache)
    echo 3. Jalankan script ini lagi
    echo.
    set PHP_OK=0
)

echo.
echo [2/3] Mengecek MySQL...
mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ MySQL ditemukan:
    mysql --version
    set MYSQL_OK=1
) else (
    echo ✗ MySQL tidak ditemukan!
    echo.
    echo SOLUSI: Install MySQL atau gunakan XAMPP
    echo 1. Jika belum install XAMPP, download dari link di atas
    echo 2. Atau install MySQL standalone dari: https://dev.mysql.com/downloads/
    echo.
    set MYSQL_OK=0
)

echo.
echo [3/3] Mengecek file project...
if exist "index.php" (
    echo ✓ File index.php ditemukan
    set FILES_OK=1
) else (
    echo ✗ File index.php tidak ditemukan
    echo Pastikan Anda menjalankan script ini dari folder project
    set FILES_OK=0
)

if exist "database.sql" (
    echo ✓ File database.sql ditemukan
) else (
    echo ✗ File database.sql tidak ditemukan
    set FILES_OK=0
)

echo.
echo ========================================
echo           HASIL PEMERIKSAAN
echo ========================================

if "%PHP_OK%"=="1" if "%MYSQL_OK%"=="1" if "%FILES_OK%"=="1" (
    echo ✓ SEMUA PERSYARATAN TERPENUHI!
    echo.
    echo Langkah selanjutnya:
    echo 1. Jalankan: setup-mysql.bat
    echo 2. Jalankan: start-server.bat
    echo 3. Buka browser: http://localhost:8000
    echo.
) else (
    echo ✗ ADA PERSYARATAN YANG BELUM TERPENUHI
    echo.
    echo REKOMENDASI: Install XAMPP (All-in-One Solution)
    echo 1. Download XAMPP: https://www.apachefriends.org/download.html
    echo 2. Install XAMPP
    echo 3. Start Apache dan MySQL dari XAMPP Control Panel
    echo 4. Jalankan script ini lagi
    echo.
)

pause
