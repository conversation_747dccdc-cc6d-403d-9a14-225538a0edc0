<?php
echo "=== TEST DATABASE CONNECTION ===\n";
echo "Testing database connection...\n\n";

$host = "localhost";
$username = "root";
$password = "";
$dbName = "spp";

try {
    // Test connection
    $conn = new mysqli($host, $username, $password, $dbName);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "✓ Database connection: SUCCESS\n";
    
    // Check tables
    $tables = ['kelas', 'petugas', 'siswa', 'spp', 'pembayaran'];
    $tableCount = 0;
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $tableCount++;
            echo "✓ Table '$table': EXISTS\n";
        } else {
            echo "✗ Table '$table': MISSING\n";
        }
    }
    
    if ($tableCount == count($tables)) {
        echo "\n✓ ALL TABLES FOUND ($tableCount/" . count($tables) . ")\n";
        
        // Check sample data
        $petugasCount = $conn->query("SELECT COUNT(*) as count FROM petugas")->fetch_assoc()['count'];
        echo "✓ Sample data: $petugasCount users found\n";
        
        if ($petugasCount > 0) {
            echo "\n=== LOGIN ACCOUNTS ===\n";
            $result = $conn->query("SELECT username, level FROM petugas");
            while ($row = $result->fetch_assoc()) {
                echo "• Username: {$row['username']} (Level: {$row['level']})\n";
            }
            echo "\n✓ DATABASE SETUP: COMPLETE!\n";
            echo "✓ Ready to start server!\n";
        }
    } else {
        echo "\n✗ MISSING TABLES: Please import database.sql\n";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    echo "✗ Please check:\n";
    echo "  1. XAMPP MySQL is running\n";
    echo "  2. Database 'spp' is imported\n";
}

echo "\n=== END TEST ===\n";
?>
