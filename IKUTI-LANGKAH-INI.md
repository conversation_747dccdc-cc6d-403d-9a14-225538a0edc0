# 🚀 CARA MENJAL<PERSON>KAN APLIKASI SPP - STEP BY STEP

## ⚡ LANGKAH SUPER MUDAH (Ikuti Urutan Ini!)

### 🔥 LANGKAH 1: INSTALL XAMPP (WAJIB!)
1. **Download XAMPP:** https://www.apachefriends.org/download.html
2. **Install XAMPP** (klik Next-Next sampai selesai)
3. **Buka XAMPP Control Panel**
4. **Klik START untuk:**
   - ✅ **Apache** (harus hijau)
   - ✅ **MySQL** (harus hijau)

### 📥 LANGKAH 2: IMPORT DATABASE
**Di PowerShell yang sudah terbuka, ketik:**
```powershell
.\import-database.bat
```

**Jika gagal, import manual:**
1. Buka browser: http://localhost/phpmyadmin
2. Klik **"Import"**
3. **Choose File** → pilih **"database.sql"**
4. <PERSON><PERSON> **"Go"**

### 🚀 LANGKAH 3: JAL<PERSON>KA<PERSON> SERVER
**Di PowerShell, ketik:**
```powershell
.\start-server.bat
```

**Atau manual:**
```powershell
php -S localhost:8000
```

### 🌐 LANGKAH 4: BUKA APLIKASI
1. **Buka browser** (Chrome/Firefox/Edge)
2. **Ketik:** http://localhost:8000
3. **Login dengan:**
   - Username: **admin**
   - Password: **admin**

---

## 🧪 TEST & VERIFIKASI

### Cek Setup Sistem:
```powershell
php test-setup.php
```
**Atau buka di browser:** http://localhost:8000/test-setup.php

---

## ❌ TROUBLESHOOTING

### "PHP tidak ditemukan"
- ✅ Install XAMPP (Langkah 1)
- ✅ Restart PowerShell setelah install

### "MySQL tidak ditemukan"
- ✅ Pastikan MySQL di XAMPP sudah **START** (hijau)
- ✅ Restart XAMPP jika perlu

### "Database tidak ada"
- ✅ Jalankan `.\import-database.bat` lagi
- ✅ Atau import manual via phpMyAdmin

### "Tidak bisa akses localhost:8000"
- ✅ Pastikan server sudah jalan (`.\start-server.bat`)
- ✅ Coba port lain: `php -S localhost:8080`

### "Login gagal"
- ✅ Pastikan database sudah diimport
- ✅ Username: **admin**, Password: **admin**

---

## 🎯 FITUR YANG BISA DICOBA

Setelah login berhasil:
- 📊 **Dashboard** - Ringkasan data
- 👥 **Data Siswa** - Kelola siswa
- 🏫 **Data Kelas** - Kelola kelas
- 💰 **Data SPP** - Atur nominal SPP
- 💳 **Pembayaran** - Input pembayaran
- 📄 **Laporan** - Generate laporan
- 🔍 **Cek Pembayaran** - Siswa cek via NISN

---

## 👤 AKUN LOGIN

**ADMIN (Full Access):**
- Username: `admin`
- Password: `admin`

**PETUGAS (Limited Access):**
- Username: `petugas`
- Password: `petugas`

---

## 🆘 BANTUAN TAMBAHAN

**File bantuan yang tersedia:**
- `MULAI-DISINI.txt` - Panduan singkat
- `test-setup.php` - Test sistem
- `setup-check.php` - Cek persyaratan
- `PANDUAN-MENJALANKAN.md` - Panduan lengkap

**Script otomatis:**
- `jalankan-aplikasi.ps1` - Setup otomatis
- `import-database.bat` - Import database
- `start-server.bat` - Start server

---

## ✅ CHECKLIST SUKSES

- [ ] XAMPP terinstall
- [ ] Apache & MySQL running (hijau)
- [ ] Database diimport
- [ ] Server berjalan
- [ ] Bisa login ke aplikasi

**🎉 SELAMAT! Aplikasi SPP siap digunakan!**
