🚀 CARA MENJALANKAN APLIKASI SPP - IKUTI LANGKAH INI!

========================================
   LANGKAH 1: INSTALL XAMPP (WAJIB!)
========================================

1. Buka browser dan download XAMPP:
   https://www.apachefriends.org/download.html

2. Install XAMPP (klik Next-Next sampai selesai)

3. Buka XAMPP Control Panel

4. Klik tombol "Start" untuk:
   ✓ Apache 
   ✓ MySQL

5. Pastikan kedua service berwarna HIJAU

========================================
   LANGKAH 2: IMPORT DATABASE
========================================

Di PowerShell yang sudah terbuka, ketik:

   .\import-database.bat

Atau jika gagal, import manual:
1. Buka browser: http://localhost/phpmyadmin
2. Klik "Import"
3. Pilih file "database.sql" 
4. Klik "Go"

========================================
   LANGKAH 3: JALANKAN SERVER
========================================

Di PowerShell, ketik:

   .\start-server.bat

Atau manual:

   php -S localhost:8000

========================================
   LANGKAH 4: BUKA APLIKASI
========================================

1. Buka browser (Chrome/Firefox/Edge)

2. Ketik alamat: http://localhost:8000

3. Login dengan:
   • Username: admin
   • Password: admin

========================================
   JIKA ADA MASALAH
========================================

❌ "PHP tidak ditemukan"
   → Install XAMPP dulu (Langkah 1)

❌ "MySQL tidak ditemukan" 
   → Pastikan MySQL di XAMPP sudah Start

❌ "Database tidak ada"
   → Jalankan import-database.bat lagi

❌ "Tidak bisa akses localhost:8000"
   → Pastikan server sudah jalan (Langkah 3)

❌ "Login gagal"
   → Cek database sudah diimport
   → Username: admin, Password: admin

========================================
   FITUR APLIKASI YANG BISA DICOBA
========================================

Setelah login berhasil:
✓ Dashboard - Lihat ringkasan data
✓ Data Siswa - Kelola data siswa
✓ Data Kelas - Kelola data kelas  
✓ Data SPP - Atur nominal SPP
✓ Pembayaran - Input pembayaran SPP
✓ Laporan - Generate laporan

========================================
   AKUN LOGIN YANG TERSEDIA
========================================

ADMIN:
Username: admin
Password: admin
(Bisa akses semua fitur)

PETUGAS:  
Username: petugas
Password: petugas
(Akses terbatas)

========================================

🎉 SELAMAT! Aplikasi SPP siap digunakan!

Jika masih ada masalah, cek file:
- setup-check.php (test sistem)
- test-connection.php (test database)
- PANDUAN-MENJALANKAN.md (panduan lengkap)
