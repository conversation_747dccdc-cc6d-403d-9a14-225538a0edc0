<?php
// Test setup aplikasi SPP
echo "<!DOCTYPE html>";
echo "<html><head><title>Test Setup - Aplikasi SPP</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
h1 { color: #333; text-align: center; }
h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
.status { font-weight: bold; }
.next-steps { background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🚀 Test Setup Aplikasi SPP</h1>";

$allOK = true;

// Test 1: PHP Version
echo "<h2>1. PHP Version Check</h2>";
$phpVersion = phpversion();
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<div class='success'>✅ PHP Version: $phpVersion (OK)</div>";
} else {
    echo "<div class='error'>❌ PHP Version: $phpVersion (Minimum: 7.4.0)</div>";
    $allOK = false;
}

// Test 2: Required Extensions
echo "<h2>2. PHP Extensions</h2>";
$extensions = ['mysqli', 'session', 'json'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✅ $ext extension loaded</div>";
    } else {
        echo "<div class='error'>❌ $ext extension missing</div>";
        $allOK = false;
    }
}

// Test 3: File Check
echo "<h2>3. Project Files</h2>";
$files = ['index.php', 'database.sql', 'app/core/Connection.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $file found</div>";
    } else {
        echo "<div class='error'>❌ $file missing</div>";
        $allOK = false;
    }
}

// Test 4: Database Connection
echo "<h2>4. Database Connection</h2>";
$host = "localhost";
$username = "root";
$password = "";
$dbName = "spp";

try {
    $conn = new mysqli($host, $username, $password);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "<div class='success'>✅ MySQL connection successful</div>";
    
    // Check database
    $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbName'");
    if ($result->num_rows > 0) {
        echo "<div class='success'>✅ Database '$dbName' exists</div>";
        
        $conn->select_db($dbName);
        
        // Check tables
        $tables = ['kelas', 'petugas', 'siswa', 'spp', 'pembayaran'];
        $tableCount = 0;
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                $tableCount++;
            }
        }
        
        if ($tableCount == count($tables)) {
            echo "<div class='success'>✅ All tables exist ($tableCount/".count($tables).")</div>";
            
            // Check sample data
            $petugasCount = $conn->query("SELECT COUNT(*) as count FROM petugas")->fetch_assoc()['count'];
            if ($petugasCount > 0) {
                echo "<div class='success'>✅ Sample data found ($petugasCount users)</div>";
                
                // Show login accounts
                echo "<div class='info'>";
                echo "<strong>Available Login Accounts:</strong><br>";
                $result = $conn->query("SELECT username, level FROM petugas");
                while ($row = $result->fetch_assoc()) {
                    echo "• Username: <strong>{$row['username']}</strong> (Level: {$row['level']})<br>";
                }
                echo "<em>Default password for all accounts is same as username</em>";
                echo "</div>";
            } else {
                echo "<div class='warning'>⚠️ No sample data found</div>";
            }
        } else {
            echo "<div class='error'>❌ Missing tables ($tableCount/".count($tables)." found)</div>";
            echo "<div class='warning'>Please import database.sql file</div>";
            $allOK = false;
        }
    } else {
        echo "<div class='error'>❌ Database '$dbName' not found</div>";
        echo "<div class='warning'>Please import database.sql file</div>";
        $allOK = false;
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
    echo "<div class='warning'>Make sure MySQL is running and import database.sql</div>";
    $allOK = false;
}

// Summary
echo "<h2>5. Setup Summary</h2>";
if ($allOK) {
    echo "<div class='success'>";
    echo "<h3>🎉 SETUP BERHASIL!</h3>";
    echo "<p>Semua persyaratan terpenuhi. Aplikasi siap digunakan!</p>";
    echo "</div>";
    
    echo "<div class='next-steps'>";
    echo "<h3>🚀 Langkah Selanjutnya:</h3>";
    echo "<ol>";
    echo "<li><strong>Akses aplikasi:</strong> <a href='index.php' target='_blank'>http://localhost:8000</a></li>";
    echo "<li><strong>Login dengan:</strong>";
    echo "<ul>";
    echo "<li>Admin: username=<code>admin</code>, password=<code>admin</code></li>";
    echo "<li>Petugas: username=<code>petugas</code>, password=<code>petugas</code></li>";
    echo "</ul></li>";
    echo "<li><strong>Mulai gunakan fitur:</strong> Dashboard, Data Siswa, Pembayaran, dll</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ Setup Belum Lengkap</h3>";
    echo "<p>Ada beberapa masalah yang perlu diperbaiki.</p>";
    echo "</div>";
    
    echo "<div class='next-steps'>";
    echo "<h3>🔧 Yang Perlu Dilakukan:</h3>";
    echo "<ol>";
    echo "<li><strong>Install XAMPP:</strong> <a href='https://www.apachefriends.org/download.html' target='_blank'>Download XAMPP</a></li>";
    echo "<li><strong>Start MySQL:</strong> Buka XAMPP Control Panel, start MySQL</li>";
    echo "<li><strong>Import Database:</strong> Jalankan <code>import-database.bat</code> atau import via phpMyAdmin</li>";
    echo "<li><strong>Test lagi:</strong> Refresh halaman ini</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test dilakukan pada: " . date('Y-m-d H:i:s') . "</em></p>";
echo "</div></body></html>";
?>
