@echo off
title Restart XAMPP Services
color 0C

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    RESTART XAMPP SERVICES                    ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo  [1] Stopping XAMPP Services...
echo.

REM Stop Apache
taskkill /F /IM httpd.exe >nul 2>&1
if %ERRORLEVEL%==0 (
    echo  ✓ Apache stopped
) else (
    echo  ℹ Apache was not running
)

REM Stop MySQL
taskkill /F /IM mysqld.exe >nul 2>&1
if %ERRORLEVEL%==0 (
    echo  ✓ MySQL stopped
) else (
    echo  ℹ MySQL was not running
)

echo.
echo  [2] Waiting 5 seconds...
timeout /t 5 /nobreak >nul

echo.
echo  [3] Starting XAMPP Services...
echo.

REM Start Apache
if exist "C:\xampp\apache\bin\httpd.exe" (
    start "" "C:\xampp\apache\bin\httpd.exe"
    echo  ✓ Apache starting...
) else (
    echo  ✗ Apache not found at C:\xampp\apache\bin\httpd.exe
)

REM Start MySQL
if exist "C:\xampp\mysql\bin\mysqld.exe" (
    start "" "C:\xampp\mysql\bin\mysqld.exe" --defaults-file="C:\xampp\mysql\bin\my.ini" --standalone --console
    echo  ✓ MySQL starting...
) else (
    echo  ✗ MySQL not found at C:\xampp\mysql\bin\mysqld.exe
)

echo.
echo  [4] Waiting for services to start...
timeout /t 10 /nobreak >nul

echo.
echo  [5] Checking status...
echo.

REM Check Apache
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ Apache is running
) else (
    echo  ✗ Apache failed to start
)

REM Check MySQL
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ MySQL is running
) else (
    echo  ✗ MySQL failed to start
)

echo.
echo  [6] Testing phpMyAdmin...
echo.

timeout /t 3 /nobreak >nul
start http://localhost/phpmyadmin

echo  📋 phpMyAdmin should open in browser
echo  📋 If still error, try:
echo     - http://localhost:8080/phpmyadmin
echo     - Restart computer
echo     - Run XAMPP as Administrator
echo.

pause
