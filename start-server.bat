@echo off
echo ========================================
echo    Aplikasi SPP - Starting Server
echo ========================================
echo.

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP tidak ditemukan!
    echo.
    echo SOLUSI MUDAH:
    echo 1. Install XAMPP: https://www.apachefriends.org/download.html
    echo 2. Start Apache dari XAMPP Control Panel
    echo 3. Copy folder project ke: C:\xampp\htdocs\
    echo 4. Akses: http://localhost/aplikasi-spp
    echo.
    echo ATAU install PHP standalone:
    echo 1. Download PHP: https://www.php.net/downloads
    echo 2. Extract ke C:\php
    echo 3. Tambahkan C:\php ke PATH
    echo.
    pause
    exit /b 1
)

echo ✓ PHP ditemukan:
php --version | findstr "PHP"
echo.

echo Memulai PHP Development Server...
echo.
echo ========================================
echo   SERVER BERJALAN DI: http://localhost:8000
echo ========================================
echo.
echo CARA MENGGUNAKAN:
echo 1. Buka browser
echo 2. Akses: http://localhost:8000
echo 3. Login dengan:
echo    • Admin: admin/admin
echo    • Petugas: petugas/petugas
echo.
echo Tekan Ctrl+C untuk menghentikan server
echo ========================================
echo.

REM Start PHP built-in server
php -S localhost:8000
