@echo off
echo ========================================
echo    Aplikasi SPP - Starting Server
echo ========================================
echo.

REM Check if PHP is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP tidak ditemukan!
    echo Pastikan PHP sudah terinstall dan ada di PATH
    echo.
    echo Untuk install PHP:
    echo 1. Download PHP dari https://www.php.net/downloads
    echo 2. Extract ke folder (misal C:\php)
    echo 3. Tambahkan C:\php ke PATH environment variable
    echo.
    pause
    exit /b 1
)

echo PHP ditemukan:
php --version | findstr "PHP"
echo.

echo Memulai PHP Development Server...
echo Server akan berjalan di: http://localhost:8000
echo.
echo Tekan Ctrl+C untuk menghentikan server
echo ========================================
echo.

REM Start PHP built-in server
php -S localhost:8000

pause
