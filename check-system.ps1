Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    CEK PERSYARATAN SISTEM - SPP APP" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check PHP
Write-Host "[1/3] Mengecek PHP..." -ForegroundColor Yellow
try {
    $phpVersion = php --version 2>$null
    if ($phpVersion) {
        Write-Host "✓ PHP ditemukan:" -ForegroundColor Green
        Write-Host $phpVersion.Split("`n")[0] -ForegroundColor Green
        $phpOK = $true
    } else {
        throw "PHP not found"
    }
} catch {
    Write-Host "✗ PHP tidak ditemukan!" -ForegroundColor Red
    Write-Host ""
    Write-Host "SOLUSI: Install PHP atau XAMPP" -ForegroundColor Yellow
    Write-Host "1. Download XAMPP dari: https://www.apachefriends.org/download.html" -ForegroundColor White
    Write-Host "2. Install XAMPP (include PHP, MySQL, Apache)" -ForegroundColor White
    Write-Host "3. Jalankan script ini lagi" -ForegroundColor White
    $phpOK = $false
}

Write-Host ""

# Check MySQL
Write-Host "[2/3] Mengecek MySQL..." -ForegroundColor Yellow
try {
    $mysqlVersion = mysql --version 2>$null
    if ($mysqlVersion) {
        Write-Host "✓ MySQL ditemukan:" -ForegroundColor Green
        Write-Host $mysqlVersion -ForegroundColor Green
        $mysqlOK = $true
    } else {
        throw "MySQL not found"
    }
} catch {
    Write-Host "✗ MySQL tidak ditemukan!" -ForegroundColor Red
    Write-Host ""
    Write-Host "SOLUSI: Install MySQL atau gunakan XAMPP" -ForegroundColor Yellow
    Write-Host "1. Jika belum install XAMPP, download dari link di atas" -ForegroundColor White
    Write-Host "2. Atau install MySQL standalone dari: https://dev.mysql.com/downloads/" -ForegroundColor White
    $mysqlOK = $false
}

Write-Host ""

# Check Project Files
Write-Host "[3/3] Mengecek file project..." -ForegroundColor Yellow
$filesOK = $true

if (Test-Path "index.php") {
    Write-Host "✓ File index.php ditemukan" -ForegroundColor Green
} else {
    Write-Host "✗ File index.php tidak ditemukan" -ForegroundColor Red
    Write-Host "Pastikan Anda menjalankan script ini dari folder project" -ForegroundColor Red
    $filesOK = $false
}

if (Test-Path "database.sql") {
    Write-Host "✓ File database.sql ditemukan" -ForegroundColor Green
} else {
    Write-Host "✗ File database.sql tidak ditemukan" -ForegroundColor Red
    $filesOK = $false
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           HASIL PEMERIKSAAN" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($phpOK -and $mysqlOK -and $filesOK) {
    Write-Host "✓ SEMUA PERSYARATAN TERPENUHI!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Langkah selanjutnya:" -ForegroundColor Yellow
    Write-Host "1. Jalankan: .\setup-mysql.ps1" -ForegroundColor White
    Write-Host "2. Jalankan: .\start-server.ps1" -ForegroundColor White
    Write-Host "3. Buka browser: http://localhost:8000" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "✗ ADA PERSYARATAN YANG BELUM TERPENUHI" -ForegroundColor Red
    Write-Host ""
    Write-Host "REKOMENDASI: Install XAMPP (All-in-One Solution)" -ForegroundColor Yellow
    Write-Host "1. Download XAMPP: https://www.apachefriends.org/download.html" -ForegroundColor White
    Write-Host "2. Install XAMPP" -ForegroundColor White
    Write-Host "3. Start Apache dan MySQL dari XAMPP Control Panel" -ForegroundColor White
    Write-Host "4. Jalankan script ini lagi" -ForegroundColor White
    Write-Host ""
}

Read-Host "Tekan Enter untuk melanjutkan"
