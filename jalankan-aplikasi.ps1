Write-Host "🚀 SETUP APLIKASI SPP - OTOMATIS" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Step 1: Check PHP
Write-Host "📋 [1/4] Mengecek PHP..." -ForegroundColor Yellow
if (Test-Command "php") {
    $phpVersion = php --version 2>$null | Select-Object -First 1
    Write-Host "✅ PHP ditemukan: $phpVersion" -ForegroundColor Green
    $phpOK = $true
} else {
    Write-Host "❌ PHP tidak ditemukan!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 SOLUSI:" -ForegroundColor Yellow
    Write-Host "1. Download XAMPP: https://www.apachefriends.org/download.html" -ForegroundColor White
    Write-Host "2. Install XAMPP" -ForegroundColor White
    Write-Host "3. Start Apache dan MySQL dari XAMPP Control Panel" -ForegroundColor White
    Write-Host "4. Jalankan script ini lagi" -ForegroundColor White
    Write-Host ""
    Read-Host "Tekan Enter setelah install XAMPP"
    return
}

# Step 2: Check MySQL
Write-Host ""
Write-Host "📋 [2/4] Mengecek MySQL..." -ForegroundColor Yellow
if (Test-Command "mysql") {
    $mysqlVersion = mysql --version 2>$null
    Write-Host "✅ MySQL ditemukan: $mysqlVersion" -ForegroundColor Green
    $mysqlOK = $true
} else {
    Write-Host "❌ MySQL tidak ditemukan!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 SOLUSI:" -ForegroundColor Yellow
    Write-Host "1. Pastikan XAMPP MySQL sudah Start (hijau)" -ForegroundColor White
    Write-Host "2. Atau install MySQL standalone" -ForegroundColor White
    Write-Host ""
    $choice = Read-Host "Lanjut import via phpMyAdmin? (y/n)"
    if ($choice -eq "y") {
        Write-Host "📂 Buka browser: http://localhost/phpmyadmin" -ForegroundColor Cyan
        Write-Host "📂 Klik Import > Pilih database.sql > Go" -ForegroundColor Cyan
        Start-Process "http://localhost/phpmyadmin"
        Read-Host "Tekan Enter setelah import selesai"
    }
    $mysqlOK = $false
}

# Step 3: Import Database
Write-Host ""
Write-Host "📋 [3/4] Import Database..." -ForegroundColor Yellow
if ($mysqlOK) {
    try {
        Write-Host "📥 Mengimport database.sql..." -ForegroundColor Cyan
        $result = mysql -u root -e "SOURCE database.sql" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database berhasil diimport!" -ForegroundColor Green
        } else {
            throw "Import failed"
        }
    } catch {
        Write-Host "❌ Import otomatis gagal" -ForegroundColor Red
        Write-Host "🔧 Import manual via phpMyAdmin:" -ForegroundColor Yellow
        Write-Host "1. Buka: http://localhost/phpmyadmin" -ForegroundColor White
        Write-Host "2. Import file database.sql" -ForegroundColor White
        Start-Process "http://localhost/phpmyadmin"
        Read-Host "Tekan Enter setelah import manual selesai"
    }
} else {
    Write-Host "⚠️ Lewati import database (MySQL tidak tersedia)" -ForegroundColor Yellow
}

# Step 4: Start Server
Write-Host ""
Write-Host "📋 [4/4] Menjalankan Server..." -ForegroundColor Yellow
if ($phpOK) {
    Write-Host "🚀 Starting PHP Development Server..." -ForegroundColor Cyan
    Write-Host ""
    Write-Host "=================================" -ForegroundColor Green
    Write-Host "✅ SERVER BERJALAN!" -ForegroundColor Green
    Write-Host "🌐 URL: http://localhost:8000" -ForegroundColor Green
    Write-Host "👤 Login: admin/admin" -ForegroundColor Green
    Write-Host "=================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Tekan Ctrl+C untuk stop server" -ForegroundColor Yellow
    Write-Host ""
    
    # Open browser
    Start-Process "http://localhost:8000"
    
    # Start PHP server
    php -S localhost:8000
} else {
    Write-Host "❌ Tidak bisa start server (PHP tidak tersedia)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 ALTERNATIF - Gunakan XAMPP:" -ForegroundColor Yellow
    Write-Host "1. Copy folder project ke: C:\xampp\htdocs\" -ForegroundColor White
    Write-Host "2. Akses: http://localhost/aplikasi-spp" -ForegroundColor White
}

Write-Host ""
Read-Host "Tekan Enter untuk keluar"
