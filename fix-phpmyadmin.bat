@echo off
title Fix phpMyAdmin - Aplikasi SPP
color 0E

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    FIX PHPMYADMIN ERROR                      ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo  [1] Checking XAMPP MySQL Status...
echo.

REM Check if MySQL is running
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ MySQL process is running
    set MYSQL_RUNNING=1
) else (
    echo  ✗ MySQL process is NOT running
    set MYSQL_RUNNING=0
)

REM Check if MySQL port is listening
netstat -an | find "3306" >NUL
if "%ERRORLEVEL%"=="0" (
    echo  ✓ MySQL port 3306 is listening
    set MYSQL_PORT=1
) else (
    echo  ✗ MySQL port 3306 is NOT listening
    set MYSQL_PORT=0
)

echo.

if %MYSQL_RUNNING%==0 (
    echo  ❌ MASALAH: MySQL tidak berjalan!
    echo.
    echo  🔧 SOLUSI:
    echo  1. Buka XAMPP Control Panel
    echo  2. Klik "Start" untuk MySQL
    echo  3. Tunggu sampai status menjadi hijau
    echo  4. Jalankan script ini lagi
    echo.
    goto end
)

if %MYSQL_PORT%==0 (
    echo  ❌ MASALAH: MySQL berjalan tapi port 3306 tidak aktif!
    echo.
    echo  🔧 SOLUSI:
    echo  1. Stop MySQL di XAMPP Control Panel
    echo  2. Tunggu 5 detik
    echo  3. Start MySQL lagi
    echo  4. Atau restart XAMPP
    echo.
    goto end
)

echo  [2] Testing MySQL Connection...
echo.

REM Test MySQL connection
if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo  Testing connection to MySQL...
    "C:\xampp\mysql\bin\mysql.exe" -u root -e "SELECT 'Connection OK' as status;" 2>NUL
    if %ERRORLEVEL%==0 (
        echo  ✓ MySQL connection: SUCCESS
        set MYSQL_CONN=1
    ) else (
        echo  ✗ MySQL connection: FAILED
        set MYSQL_CONN=0
    )
) else (
    echo  ✗ MySQL client not found at C:\xampp\mysql\bin\mysql.exe
    set MYSQL_CONN=0
)

echo.

if %MYSQL_CONN%==1 (
    echo  [3] Opening phpMyAdmin...
    echo.
    echo  ✅ MySQL OK - Opening phpMyAdmin
    start http://localhost/phpmyadmin
    echo.
    echo  📋 Jika masih error, coba:
    echo  1. Refresh browser (F5)
    echo  2. Clear browser cache
    echo  3. Coba http://localhost:8080/phpmyadmin
    echo  4. Restart XAMPP
) else (
    echo  [3] MySQL Connection Issues
    echo.
    echo  🔧 LANGKAH PERBAIKAN:
    echo.
    echo  A. RESTART MYSQL:
    echo     1. Buka XAMPP Control Panel
    echo     2. Stop MySQL
    echo     3. Tunggu 10 detik
    echo     4. Start MySQL lagi
    echo.
    echo  B. RESTART XAMPP:
    echo     1. Close XAMPP Control Panel
    echo     2. Run as Administrator
    echo     3. Start Apache dan MySQL
    echo.
    echo  C. CHECK PORT CONFLICT:
    echo     1. Buka XAMPP Control Panel
    echo     2. Klik "Config" di MySQL
    echo     3. Pilih "my.ini"
    echo     4. Cari "port = 3306"
    echo     5. Ganti ke "port = 3307" jika ada konflik
    echo.
)

:end
echo.
pause
